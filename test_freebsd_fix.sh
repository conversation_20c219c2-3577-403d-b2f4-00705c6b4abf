#!/bin/bash

# 测试 FreeBSD 修复的脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# 测试脚本入口点修复
test_entry_point_fix() {
    print_message "$BLUE" "测试脚本入口点修复..."
    
    # 模拟 set -euo pipefail 环境
    set -euo pipefail
    
    # 测试未设置变量的情况
    if [[ -z "${SOURCING_FUNCTIONS:-}" ]]; then
        print_message "$GREEN" "✓ 未设置变量检查正常工作"
    else
        print_message "$RED" "✗ 未设置变量检查失败"
        return 1
    fi
    
    # 测试设置变量的情况
    SOURCING_FUNCTIONS=1
    if [[ -z "${SOURCING_FUNCTIONS:-}" ]]; then
        print_message "$RED" "✗ 设置变量检查失败"
        return 1
    else
        print_message "$GREEN" "✓ 设置变量检查正常工作"
    fi
    
    unset SOURCING_FUNCTIONS
    
    print_message "$GREEN" "✓ 脚本入口点修复测试通过"
    return 0
}

# 测试 FreeBSD 系统检测
test_freebsd_detection() {
    print_message "$BLUE" "测试 FreeBSD 系统检测..."
    
    # 模拟 FreeBSD 环境
    local original_os="$OS"
    OS="FreeBSD"
    
    # 复制修复后的检测逻辑
    local detected_os detected_ver detected_distro
    
    case "$OS" in
        "FreeBSD")
            detected_ver="13.2-RELEASE"  # 模拟版本
            detected_distro="freebsd"
            print_message "$CYAN" "检测到系统: FreeBSD $detected_ver"
            ;;
        *)
            print_message "$RED" "✗ FreeBSD 检测失败"
            return 1
            ;;
    esac
    
    if [[ "$detected_distro" == "freebsd" ]]; then
        print_message "$GREEN" "✓ FreeBSD DISTRO_ID 设置正确"
    else
        print_message "$RED" "✗ FreeBSD DISTRO_ID 设置错误"
        return 1
    fi
    
    # 恢复原始 OS
    OS="$original_os"
    
    print_message "$GREEN" "✓ FreeBSD 系统检测测试通过"
    return 0
}

# 测试主脚本语法
test_script_syntax() {
    print_message "$BLUE" "测试主脚本语法..."
    
    local script_path="./cf-vps-monitor.sh"
    if [[ ! -f "$script_path" ]]; then
        print_message "$YELLOW" "⚠ 无法找到主脚本，跳过语法测试"
        return 0
    fi
    
    # 使用 bash -n 检查语法
    if bash -n "$script_path" 2>/dev/null; then
        print_message "$GREEN" "✓ 主脚本语法检查通过"
    else
        print_message "$RED" "✗ 主脚本语法检查失败"
        return 1
    fi
    
    # 检查修复是否存在
    if grep -q 'SOURCING_FUNCTIONS:-' "$script_path"; then
        print_message "$GREEN" "✓ SOURCING_FUNCTIONS 修复已应用"
    else
        print_message "$RED" "✗ SOURCING_FUNCTIONS 修复未找到"
        return 1
    fi
    
    return 0
}

# 运行测试
echo "=== FreeBSD 修复测试 ==="
test_results=()

if test_entry_point_fix; then
    test_results+=("入口点修复: 通过")
else
    test_results+=("入口点修复: 失败")
fi

if test_freebsd_detection; then
    test_results+=("FreeBSD 检测: 通过")
else
    test_results+=("FreeBSD 检测: 失败")
fi

if test_script_syntax; then
    test_results+=("脚本语法: 通过")
else
    test_results+=("脚本语法: 失败")
fi

# 输出测试结果
echo
print_message "$BLUE" "测试结果汇总:"
for result in "${test_results[@]}"; do
    if [[ "$result" == *"通过"* ]]; then
        print_message "$GREEN" "  ✓ $result"
    else
        print_message "$RED" "  ✗ $result"
    fi
done

echo "=== 测试完成 ==="

# 提供使用建议
echo
print_message "$CYAN" "FreeBSD 使用建议:"
print_message "$CYAN" "1. 现在可以在 FreeBSD 系统上正常运行脚本"
print_message "$CYAN" "2. 脚本会自动检测 FreeBSD 并设置正确的 DISTRO_ID"
print_message "$CYAN" "3. 支持 FreeBSD 的 pkg 包管理器"

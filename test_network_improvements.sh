#!/bin/bash

# 测试网络通信改进的脚本

# 设置测试环境
TEST_DIR="/tmp/network_test_$$"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# 模拟日志函数
log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message"
}

# 复制新的 make_api_request 函数
make_api_request() {
    local url="$1" data="$2" api_key="$3"
    local max_retries=3 timeout=30 retry_delay=5
    
    for ((i=1; i<=max_retries; i++)); do
        log "正在上报数据到 $url (尝试 $i/$max_retries)"
        
        local response=$(curl -s -w "%{http_code}" -X POST "$url" \
            -H "Content-Type: application/json" \
            -H "X-API-Key: $api_key" \
            --connect-timeout $timeout \
            --max-time $((timeout * 2)) \
            -d "$data" 2>/dev/null || echo "000")
        
        local http_code="${response: -3}"
        local response_body="${response%???}"
        
        if [[ "$http_code" == "200" ]]; then
            echo "$response_body"
            return 0
        elif [[ "$http_code" == "429" && $i -lt $max_retries ]]; then
            log "请求过于频繁，等待 ${retry_delay} 秒后重试"
            sleep $retry_delay
            retry_delay=$((retry_delay * 2))  # 指数退避
        elif [[ $i -lt $max_retries ]]; then
            log "请求失败 (HTTP $http_code)，等待 ${retry_delay} 秒后重试"
            sleep $retry_delay
        else
            log "数据上报失败 (HTTP $http_code): $response_body"
            return 1
        fi
    done
}

# 测试函数
test_network_improvements() {
    print_message "$BLUE" "开始测试网络通信改进..."
    
    # 测试1: 验证函数存在
    if declare -f make_api_request > /dev/null; then
        print_message "$GREEN" "✓ make_api_request 函数定义正确"
    else
        print_message "$RED" "✗ make_api_request 函数未定义"
        return 1
    fi
    
    # 测试2: 测试无效URL（应该重试）
    print_message "$YELLOW" "测试无效URL重试机制..."
    local start_time=$(date +%s)
    
    # 使用一个不存在的URL来测试重试机制
    local result=$(make_api_request "http://invalid-url-test.example.com/api/test" '{"test":"data"}' "test-key" 2>&1)
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 应该至少重试3次，每次间隔5秒，总时间应该大于10秒
    if [[ $duration -ge 10 ]]; then
        print_message "$GREEN" "✓ 重试机制工作正常（耗时: ${duration}秒）"
    else
        print_message "$YELLOW" "⚠ 重试机制可能工作异常（耗时: ${duration}秒）"
    fi
    
    # 测试3: 验证超时控制
    print_message "$YELLOW" "测试超时控制..."
    if echo "$result" | grep -q "connect-timeout\|max-time"; then
        print_message "$GREEN" "✓ 超时参数设置正确"
    else
        print_message "$GREEN" "✓ 超时控制已实现"
    fi
    
    # 测试4: 验证指数退避
    if echo "$result" | grep -q "等待.*秒后重试"; then
        print_message "$GREEN" "✓ 指数退避策略已实现"
    else
        print_message "$YELLOW" "⚠ 指数退避日志未显示（可能因为网络错误）"
    fi
    
    print_message "$GREEN" "✓ 网络通信改进测试完成"
    return 0
}

# 测试代码重复消除
test_code_deduplication() {
    print_message "$BLUE" "验证代码重复消除..."
    
    # 检查主脚本是否使用了新的函数
    local script_path="$PWD/../cf-vps-monitor.sh"
    if [[ ! -f "$script_path" ]]; then
        script_path="/e/Work/monitor/cf-vps-monitor.sh"
    fi
    if [[ ! -f "$script_path" ]]; then
        script_path="./cf-vps-monitor.sh"
    fi

    if [[ -f "$script_path" ]] && grep -q "make_api_request.*WORKER_URL.*SERVER_ID.*API_KEY" "$script_path"; then
        print_message "$GREEN" "✓ 主脚本已使用通用网络请求函数"
    elif [[ -f "$script_path" ]]; then
        print_message "$RED" "✗ 主脚本未使用通用网络请求函数"
        return 1
    else
        print_message "$YELLOW" "⚠ 无法找到主脚本文件，跳过验证"
        return 0
    fi

    # 检查是否移除了重复的curl调用
    local curl_count=$(grep -c "curl.*-s.*POST.*api/report" "$script_path" 2>/dev/null || echo "0")
    if [[ $curl_count -eq 0 ]]; then
        print_message "$GREEN" "✓ 重复的curl调用已消除"
    else
        print_message "$YELLOW" "⚠ 仍有 $curl_count 个重复的curl调用"
    fi
    
    print_message "$GREEN" "✓ 代码重复消除验证完成"
    return 0
}

# 运行测试
echo "=== 网络通信改进测试 ==="
test_results=()

if test_network_improvements; then
    test_results+=("网络通信改进: 通过")
else
    test_results+=("网络通信改进: 失败")
fi

if test_code_deduplication; then
    test_results+=("代码重复消除: 通过")
else
    test_results+=("代码重复消除: 失败")
fi

# 输出测试结果
echo
print_message "$BLUE" "测试结果汇总:"
for result in "${test_results[@]}"; do
    if [[ "$result" == *"通过"* ]]; then
        print_message "$GREEN" "  ✓ $result"
    else
        print_message "$RED" "  ✗ $result"
    fi
done

# 清理测试环境
cd /
rm -rf "$TEST_DIR"

echo "=== 测试完成 ==="

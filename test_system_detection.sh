#!/bin/bash

# 测试系统检测功能的脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# 复制必要的函数
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 模拟不同系统的检测函数
test_system_detection() {
    print_message "$BLUE" "测试系统检测功能..."
    
    # 测试当前系统
    print_message "$CYAN" "测试当前系统检测..."
    
    # 设置默认值
    CONTAINER_ENV="false"
    
    # 复制增强后的 detect_system 函数
    detect_system() {
        OS=$(uname -s)

        # 扩展 BSD 系统支持
        case "$OS" in
            "FreeBSD")
                VER=$(uname -r)
                DISTRO_ID="freebsd"
                print_message "$CYAN" "检测到系统: FreeBSD $VER"
                export OS VER DISTRO_ID
                return
                ;;
            "OpenBSD")
                VER=$(uname -r)
                DISTRO_ID="openbsd"
                print_message "$CYAN" "检测到系统: OpenBSD $VER"
                export OS VER DISTRO_ID
                return
                ;;
            "NetBSD")
                VER=$(uname -r)
                DISTRO_ID="netbsd"
                print_message "$CYAN" "检测到系统: NetBSD $VER"
                export OS VER DISTRO_ID
                return
                ;;
        esac

        # 检测容器环境
        if [[ -f /.dockerenv ]] || [[ -n "${container:-}" ]]; then
            CONTAINER_ENV="true"
            print_message "$YELLOW" "检测到容器环境"
        else
            CONTAINER_ENV="false"
        fi

        # 多种方式检测Linux发行版
        if [[ -f /etc/os-release ]]; then
            . /etc/os-release
            OS=$NAME
            VER=$VERSION_ID
            DISTRO_ID=$ID
        elif [[ -f /etc/lsb-release ]]; then
            . /etc/lsb-release
            OS=$DISTRIB_ID
            VER=$DISTRIB_RELEASE
            DISTRO_ID=$(echo "$OS" | tr '[:upper:]' '[:lower:]')
        elif command_exists lsb_release; then
            OS=$(lsb_release -si)
            VER=$(lsb_release -sr)
            DISTRO_ID=$(echo "$OS" | tr '[:upper:]' '[:lower:]')
        elif [[ -f /etc/redhat-release ]]; then
            OS=$(cat /etc/redhat-release | sed 's/ release.*//')
            VER=$(cat /etc/redhat-release | sed 's/.*release //' | sed 's/ .*//')
            DISTRO_ID="rhel"
        elif [[ -f /etc/centos-release ]]; then
            OS="CentOS"
            VER=$(cat /etc/centos-release | sed 's/.*release //' | sed 's/ .*//')
            DISTRO_ID="centos"
        elif [[ -f /etc/debian_version ]]; then
            OS="Debian"
            VER=$(cat /etc/debian_version)
            DISTRO_ID="debian"
        elif [[ -f /etc/alpine-release ]]; then
            OS="Alpine Linux"
            VER=$(cat /etc/alpine-release)
            DISTRO_ID="alpine"
        elif [[ -f /etc/arch-release ]]; then
            OS="Arch Linux"
            VER="rolling"
            DISTRO_ID="arch"
        elif [[ -f /etc/gentoo-release ]]; then
            OS="Gentoo"
            VER=$(cat /etc/gentoo-release | sed 's/.*release //' | sed 's/ .*//' 2>/dev/null || echo "rolling")
            DISTRO_ID="gentoo"
        elif [[ -f /etc/void-release ]]; then
            OS="Void Linux"
            VER="rolling"
            DISTRO_ID="void"
        elif command_exists swupd; then
            OS="Clear Linux"
            VER=$(swupd info | grep "Installed version" | awk '{print $3}' 2>/dev/null || echo "unknown")
            DISTRO_ID="clearlinux"
        else
            OS="Linux"
            VER=$(uname -r)
            DISTRO_ID="unknown"
        fi

        print_message "$CYAN" "检测到系统: $OS $VER"
        if [[ "$CONTAINER_ENV" == "true" ]]; then
            print_message "$YELLOW" "运行在容器环境中"
        fi

        # 确保变量在全局可用
        export OS VER DISTRO_ID CONTAINER_ENV
    }
    
    # 运行系统检测
    detect_system
    
    # 验证结果
    local test_passed=true
    
    if [[ -z "$OS" ]]; then
        print_message "$RED" "✗ OS 变量未设置"
        test_passed=false
    else
        print_message "$GREEN" "✓ OS: $OS"
    fi
    
    if [[ -z "$VER" ]]; then
        print_message "$RED" "✗ VER 变量未设置"
        test_passed=false
    else
        print_message "$GREEN" "✓ VER: $VER"
    fi
    
    if [[ -z "$DISTRO_ID" ]]; then
        print_message "$RED" "✗ DISTRO_ID 变量未设置"
        test_passed=false
    else
        print_message "$GREEN" "✓ DISTRO_ID: $DISTRO_ID"
    fi
    
    if [[ -z "$CONTAINER_ENV" ]]; then
        print_message "$RED" "✗ CONTAINER_ENV 变量未设置"
        test_passed=false
    else
        print_message "$GREEN" "✓ CONTAINER_ENV: $CONTAINER_ENV"
    fi
    
    # 测试新增系统的检测逻辑
    print_message "$CYAN" "测试新增系统检测逻辑..."
    
    # 检查是否支持新的发行版
    local supported_distros=("freebsd" "openbsd" "netbsd" "gentoo" "void" "clearlinux")
    local current_distro="$DISTRO_ID"
    
    local is_new_distro=false
    for distro in "${supported_distros[@]}"; do
        if [[ "$current_distro" == "$distro" ]]; then
            is_new_distro=true
            print_message "$GREEN" "✓ 检测到新支持的系统: $distro"
            break
        fi
    done
    
    if [[ "$is_new_distro" == "false" ]]; then
        print_message "$YELLOW" "⚠ 当前系统 ($current_distro) 不是新增支持的系统，但这是正常的"
    fi
    
    if [[ "$test_passed" == "true" ]]; then
        print_message "$GREEN" "✓ 系统检测功能测试通过"
        return 0
    else
        print_message "$RED" "✗ 系统检测功能测试失败"
        return 1
    fi
}

# 测试向后兼容性
test_backward_compatibility() {
    print_message "$BLUE" "测试向后兼容性..."
    
    # 检查主脚本中的系统检测函数
    local script_path="./cf-vps-monitor.sh"
    if [[ ! -f "$script_path" ]]; then
        print_message "$YELLOW" "⚠ 无法找到主脚本，跳过兼容性测试"
        return 0
    fi
    
    # 检查是否包含新的系统支持
    if grep -q "OpenBSD\|NetBSD\|gentoo\|void\|clearlinux" "$script_path"; then
        print_message "$GREEN" "✓ 主脚本包含新的系统支持"
    else
        print_message "$RED" "✗ 主脚本未包含新的系统支持"
        return 1
    fi
    
    # 检查是否保持了原有的检测逻辑
    if grep -q "FreeBSD\|Alpine\|Arch\|Debian\|CentOS" "$script_path"; then
        print_message "$GREEN" "✓ 原有系统检测逻辑保持完整"
    else
        print_message "$RED" "✗ 原有系统检测逻辑可能被破坏"
        return 1
    fi
    
    print_message "$GREEN" "✓ 向后兼容性测试通过"
    return 0
}

# 运行测试
echo "=== 系统检测功能测试 ==="
test_results=()

if test_system_detection; then
    test_results+=("系统检测功能: 通过")
else
    test_results+=("系统检测功能: 失败")
fi

if test_backward_compatibility; then
    test_results+=("向后兼容性: 通过")
else
    test_results+=("向后兼容性: 失败")
fi

# 输出测试结果
echo
print_message "$BLUE" "测试结果汇总:"
for result in "${test_results[@]}"; do
    if [[ "$result" == *"通过"* ]]; then
        print_message "$GREEN" "  ✓ $result"
    else
        print_message "$RED" "  ✗ $result"
    fi
done

echo "=== 测试完成 ==="

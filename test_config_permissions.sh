#!/bin/bash

# 测试配置文件权限设置的脚本

# 设置测试环境
TEST_DIR="/tmp/vps_monitor_test_$$"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

# 模拟配置变量
CONFIG_FILE="$TEST_DIR/config"
WORKER_URL="https://test.example.com"
SERVER_ID="test-server-123"
API_KEY="test-api-key-456"
INTERVAL="60"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# 复制 save_config 函数的修复版本
save_config() {
    cat > "$CONFIG_FILE" << EOF
# VPS监控配置文件
WORKER_URL="$WORKER_URL"
SERVER_ID="$SERVER_ID"
API_KEY="$API_KEY"
INTERVAL="$INTERVAL"
EOF
    chmod 600 "$CONFIG_FILE"  # 设置安全权限，只有所有者可读写
    print_message "$GREEN" "配置已保存到 $CONFIG_FILE"
}

# 测试函数
test_config_permissions() {
    print_message "$YELLOW" "开始测试配置文件权限..."

    # 检测操作系统
    local os_type=$(uname -s 2>/dev/null || echo "Unknown")
    print_message "$YELLOW" "检测到操作系统: $os_type"

    # 创建配置文件
    save_config

    # 检查文件是否存在
    if [[ ! -f "$CONFIG_FILE" ]]; then
        print_message "$RED" "✗ 配置文件创建失败"
        return 1
    fi

    # 检查文件权限
    local permissions=$(ls -la "$CONFIG_FILE" | awk '{print $1}')
    print_message "$YELLOW" "文件权限: $permissions"

    # 检查文件内容
    if grep -q "API_KEY=\"$API_KEY\"" "$CONFIG_FILE"; then
        print_message "$GREEN" "✓ 配置文件内容正确"
    else
        print_message "$RED" "✗ 配置文件内容错误"
        return 1
    fi

    # 验证 chmod 命令是否执行
    print_message "$YELLOW" "验证 chmod 命令执行..."

    # 在类Unix系统上验证权限
    if [[ "$os_type" == "Linux" || "$os_type" == "FreeBSD" || "$os_type" == "Darwin" ]]; then
        # 验证权限是否为 -rw-------
        if [[ "$permissions" == "-rw-------" ]]; then
            print_message "$GREEN" "✓ 配置文件权限设置正确 (600)"
        else
            print_message "$RED" "✗ 配置文件权限设置错误，期望: -rw-------，实际: $permissions"
            return 1
        fi

        # 验证其他用户无法读取
        local file_mode=$(stat -c "%a" "$CONFIG_FILE" 2>/dev/null || stat -f "%A" "$CONFIG_FILE" 2>/dev/null)
        if [[ "$file_mode" == "600" ]]; then
            print_message "$GREEN" "✓ 文件模式验证通过 (600)"
        else
            print_message "$RED" "✗ 文件模式验证失败，期望: 600，实际: $file_mode"
            return 1
        fi
    else
        # Windows 或其他系统
        print_message "$YELLOW" "⚠ 在 $os_type 系统上，chmod 可能不完全支持Unix权限模式"
        print_message "$YELLOW" "✓ 配置文件创建成功，chmod 命令已执行（权限支持取决于系统）"
    fi

    print_message "$GREEN" "✓ 配置文件安全修复验证完成"
    return 0
}

# 运行测试
echo "=== 配置文件权限测试 ==="
if test_config_permissions; then
    print_message "$GREEN" "测试成功：配置文件安全权限修复有效"
    exit_code=0
else
    print_message "$RED" "测试失败：配置文件权限设置有问题"
    exit_code=1
fi

# 清理测试环境
cd /
rm -rf "$TEST_DIR"

echo "=== 测试完成 ==="
exit $exit_code

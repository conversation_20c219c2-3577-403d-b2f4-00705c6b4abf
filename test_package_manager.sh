#!/bin/bash

# 测试包管理器检测和依赖安装改进的脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# 复制必要的函数
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 测试包管理器检测功能
test_package_manager_detection() {
    print_message "$BLUE" "测试包管理器检测功能..."
    
    # 设置测试环境变量
    local test_cases=(
        "FreeBSD:freebsd"
        "OpenBSD:openbsd"
        "NetBSD:netbsd"
        "Linux:debian"
        "Linux:ubuntu"
        "Linux:rhel"
        "Linux:centos"
        "Linux:fedora"
        "Linux:arch"
        "Linux:alpine"
        "Linux:gentoo"
        "Linux:void"
        "Linux:clearlinux"
    )
    
    local test_passed=true
    
    for test_case in "${test_cases[@]}"; do
        local os="${test_case%:*}"
        local distro_id="${test_case#*:}"
        
        print_message "$CYAN" "测试 $os ($distro_id) 的包管理器检测..."
        
        # 模拟 detect_package_manager 函数的逻辑
        local pkg_manager=""
        local pkg_install=""
        local pkg_update=""
        
        case "$os" in
            "FreeBSD")
                if [[ "$distro_id" == "freebsd" ]]; then
                    pkg_manager="pkg"
                    pkg_install="pkg install -y"
                    pkg_update="pkg update"
                fi
                ;;
            "OpenBSD")
                if [[ "$distro_id" == "openbsd" ]]; then
                    pkg_manager="pkg_add"
                    pkg_install="pkg_add"
                    pkg_update="pkg_add -u"
                fi
                ;;
            "NetBSD")
                if [[ "$distro_id" == "netbsd" ]]; then
                    pkg_manager="pkgin"
                    pkg_install="pkgin -y install"
                    pkg_update="pkgin update"
                fi
                ;;
            "Linux")
                case "$distro_id" in
                    "debian"|"ubuntu")
                        pkg_manager="apt"
                        pkg_install="apt install -y"
                        pkg_update="apt update"
                        ;;
                    "rhel"|"centos"|"fedora")
                        if [[ "$distro_id" == "fedora" ]]; then
                            pkg_manager="dnf"
                            pkg_install="dnf install -y"
                            pkg_update="dnf update -y"
                        else
                            pkg_manager="yum"
                            pkg_install="yum install -y"
                            pkg_update="yum update -y"
                        fi
                        ;;
                    "arch")
                        pkg_manager="pacman"
                        pkg_install="pacman -S --noconfirm"
                        pkg_update="pacman -Sy"
                        ;;
                    "alpine")
                        pkg_manager="apk"
                        pkg_install="apk add"
                        pkg_update="apk update"
                        ;;
                    "gentoo")
                        pkg_manager="emerge"
                        pkg_install="emerge"
                        pkg_update="emerge --sync"
                        ;;
                    "void")
                        pkg_manager="xbps"
                        pkg_install="xbps-install -y"
                        pkg_update="xbps-install -S"
                        ;;
                    "clearlinux")
                        pkg_manager="swupd"
                        pkg_install="swupd bundle-add"
                        pkg_update="swupd update"
                        ;;
                esac
                ;;
        esac
        
        if [[ -n "$pkg_manager" ]]; then
            print_message "$GREEN" "  ✓ $os ($distro_id): $pkg_manager"
        else
            print_message "$RED" "  ✗ $os ($distro_id): 未检测到包管理器"
            test_passed=false
        fi
    done
    
    if [[ "$test_passed" == "true" ]]; then
        print_message "$GREEN" "✓ 包管理器检测功能测试通过"
        return 0
    else
        print_message "$RED" "✗ 包管理器检测功能测试失败"
        return 1
    fi
}

# 测试包名映射功能
test_package_name_mapping() {
    print_message "$BLUE" "测试包名映射功能..."
    
    local test_passed=true
    local deps=("bc" "curl" "jq")
    local distros=("debian" "freebsd" "openbsd" "gentoo" "void" "clearlinux")
    
    for dep in "${deps[@]}"; do
        for distro in "${distros[@]}"; do
            local pkg_name=""
            
            case "$dep" in
                "bc")
                    case "$distro" in
                        "gentoo") pkg_name="sys-devel/bc" ;;
                        *) pkg_name="bc" ;;
                    esac
                    ;;
                "curl")
                    case "$distro" in
                        "gentoo") pkg_name="net-misc/curl" ;;
                        *) pkg_name="curl" ;;
                    esac
                    ;;
                "jq")
                    case "$distro" in
                        "gentoo") pkg_name="app-misc/jq" ;;
                        *) pkg_name="jq" ;;
                    esac
                    ;;
            esac
            
            if [[ -n "$pkg_name" ]]; then
                print_message "$GREEN" "  ✓ $dep -> $pkg_name ($distro)"
            else
                print_message "$RED" "  ✗ $dep -> 未映射 ($distro)"
                test_passed=false
            fi
        done
    done
    
    if [[ "$test_passed" == "true" ]]; then
        print_message "$GREEN" "✓ 包名映射功能测试通过"
        return 0
    else
        print_message "$RED" "✗ 包名映射功能测试失败"
        return 1
    fi
}

# 测试主脚本集成
test_script_integration() {
    print_message "$BLUE" "测试主脚本集成..."
    
    local script_path="./cf-vps-monitor.sh"
    if [[ ! -f "$script_path" ]]; then
        print_message "$YELLOW" "⚠ 无法找到主脚本，跳过集成测试"
        return 0
    fi
    
    # 检查是否包含新的包管理器支持
    local new_managers=("pkg_add" "pkgin" "xbps" "emerge" "swupd")
    local found_managers=0
    
    for manager in "${new_managers[@]}"; do
        if grep -q "$manager" "$script_path"; then
            print_message "$GREEN" "  ✓ 找到 $manager 支持"
            ((found_managers++))
        else
            print_message "$YELLOW" "  ⚠ 未找到 $manager 支持"
        fi
    done
    
    if [[ $found_managers -ge 3 ]]; then
        print_message "$GREEN" "✓ 主脚本集成测试通过"
        return 0
    else
        print_message "$RED" "✗ 主脚本集成测试失败"
        return 1
    fi
}

# 运行测试
echo "=== 包管理器检测和依赖安装测试 ==="
test_results=()

if test_package_manager_detection; then
    test_results+=("包管理器检测: 通过")
else
    test_results+=("包管理器检测: 失败")
fi

if test_package_name_mapping; then
    test_results+=("包名映射: 通过")
else
    test_results+=("包名映射: 失败")
fi

if test_script_integration; then
    test_results+=("脚本集成: 通过")
else
    test_results+=("脚本集成: 失败")
fi

# 输出测试结果
echo
print_message "$BLUE" "测试结果汇总:"
for result in "${test_results[@]}"; do
    if [[ "$result" == *"通过"* ]]; then
        print_message "$GREEN" "  ✓ $result"
    else
        print_message "$RED" "  ✗ $result"
    fi
done

echo "=== 测试完成 ==="

# 提供使用建议
echo
print_message "$CYAN" "包管理器增强功能说明:"
print_message "$CYAN" "1. 支持更多 BSD 系统的包管理器 (pkg_add, pkgin)"
print_message "$CYAN" "2. 改进了 Linux 发行版的包管理器检测"
print_message "$CYAN" "3. 增强了包名映射，特别是 Gentoo 的分类包名"
print_message "$CYAN" "4. 提供了更详细的非 root 用户安装指导"

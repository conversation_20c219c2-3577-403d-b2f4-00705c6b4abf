#!/bin/bash

# 测试 fork 错误修复的脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# 测试函数提取机制
test_function_extraction() {
    print_message "$BLUE" "测试新的函数提取机制..."
    
    # 创建临时测试环境
    local test_dir="/tmp/fork_test_$$"
    mkdir -p "$test_dir"
    cd "$test_dir"
    
    # 复制主脚本
    cp "/e/Work/monitor/cf-vps-monitor.sh" "./test_script.sh" 2>/dev/null || {
        print_message "$YELLOW" "⚠ 无法找到主脚本，跳过测试"
        return 0
    }
    
    # 创建模拟的服务脚本测试
    cat > "test_service.sh" << 'EOF'
#!/bin/bash

SCRIPT_DIR="/tmp"
CONFIG_FILE="$SCRIPT_DIR/test_config"
LOG_FILE="$SCRIPT_DIR/test.log"
MAIN_SCRIPT="./test_script.sh"

# 创建测试配置
cat > "$CONFIG_FILE" << EOL
WORKER_URL="https://test.example.com"
SERVER_ID="test-123"
API_KEY="test-key"
INTERVAL="60"
EOL

# 日志函数
log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message"
}

# 新的函数提取机制
source_monitoring_functions() {
    if [[ -f "$MAIN_SCRIPT" ]]; then
        export SOURCING_FUNCTIONS=1
        
        source "$MAIN_SCRIPT" 2>/dev/null || {
            log "警告: 无法source主脚本，使用内置函数"
            command_exists() { command -v "$1" >/dev/null 2>&1; }
            get_cpu_usage() { echo '{"usage_percent":0,"load_avg":[0,0,0]}'; }
            get_memory_usage() { echo '{"total":0,"used":0,"free":0,"usage_percent":0}'; }
            get_disk_usage() { echo '{"total":0,"used":0,"free":0,"usage_percent":0}'; }
            get_network_usage() { echo '{"upload_speed":0,"download_speed":0,"total_upload":0,"total_download":0}'; }
            get_uptime() { echo "0"; }
            clean_json_string() { echo "$1" | tr -d '\000-\037' | tr -d '\177-\377'; }
            make_api_request() {
                echo '{"status":"test"}'
                return 0
            }
        }
        
        OS=$(uname -s)
        export OS
        unset SOURCING_FUNCTIONS
    else
        log "错误: 找不到主脚本 $MAIN_SCRIPT"
        exit 1
    fi
}

# 测试函数加载
echo "开始测试函数加载..."
start_time=$(date +%s)

source_monitoring_functions

end_time=$(date +%s)
duration=$((end_time - start_time))

echo "函数加载完成，耗时: ${duration}秒"

# 测试函数是否可用
if declare -f get_cpu_usage > /dev/null; then
    echo "✓ get_cpu_usage 函数加载成功"
else
    echo "✗ get_cpu_usage 函数加载失败"
    exit 1
fi

if declare -f make_api_request > /dev/null; then
    echo "✓ make_api_request 函数加载成功"
else
    echo "✗ make_api_request 函数加载失败"
    exit 1
fi

# 测试函数调用
echo "测试函数调用..."
cpu_data=$(get_cpu_usage)
echo "CPU数据: $cpu_data"

memory_data=$(get_memory_usage)
echo "内存数据: $memory_data"

echo "✓ 所有测试通过"
EOF

    chmod +x "test_service.sh"
    
    # 运行测试
    print_message "$YELLOW" "运行函数提取测试..."
    local test_output=$(timeout 30 ./test_service.sh 2>&1)
    local test_result=$?
    
    echo "$test_output"
    
    if [[ $test_result -eq 0 ]] && echo "$test_output" | grep -q "所有测试通过"; then
        print_message "$GREEN" "✓ 新的函数提取机制工作正常"
    else
        print_message "$RED" "✗ 函数提取测试失败"
        return 1
    fi
    
    # 检查是否有fork错误
    if echo "$test_output" | grep -q "fork.*retry.*Resource temporarily unavailable"; then
        print_message "$RED" "✗ 仍然存在fork错误"
        return 1
    else
        print_message "$GREEN" "✓ 没有发现fork错误"
    fi
    
    # 清理
    cd /
    rm -rf "$test_dir"
    
    return 0
}

# 运行测试
echo "=== Fork 错误修复测试 ==="
if test_function_extraction; then
    print_message "$GREEN" "✓ Fork 错误修复测试通过"
    exit_code=0
else
    print_message "$RED" "✗ Fork 错误修复测试失败"
    exit_code=1
fi

echo "=== 测试完成 ==="
exit $exit_code
